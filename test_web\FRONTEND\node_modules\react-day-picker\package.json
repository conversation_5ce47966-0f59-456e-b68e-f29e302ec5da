{"name": "react-day-picker", "version": "8.10.1", "description": "Customizable Date Picker for React", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "http://react-day-picker.js.org", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/gpbl/react-day-picker"}, "bugs": {"url": "https://github.com/gpbl/react-day-picker/issues"}, "main": "dist/index.js", "module": "dist/index.esm.js", "unpkg": "dist/index.min.js", "types": "dist/index.d.ts", "style": "dist/style.css", "scripts": {"prepublish": "pnpm build", "build": "rimraf dist && rollup -c", "build-watch": "rollup -c -w", "lint": "eslint .", "test": "jest", "test-watch": "jest --watch", "typecheck": "tsc --project ./tsconfig.json --noEmit", "typecheck-watch": "tsc --project ./tsconfig.json --noEmit --watch"}, "files": ["dist", "src", "tsconfig.json"], "devDependencies": {"@jest/types": "^29.6.3", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.1", "@rollup/plugin-typescript": "^11.1.5", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.11", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/testing-library__jest-dom": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "date-fns": "^3.0.6", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mockdate": "^3.0.5", "postcss": "^8.4.32", "postcss-d-ts": "^1.2.0", "postcss-typescript-d-ts": "^1.0.0", "prettier": "^3.1.1", "react": "^18.2.0", "react-classnaming": "^0.16.4", "react-dom": "^18.2.0", "rimraf": "^5.0.1", "rollup": "^4.9.1", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-postcss": "^4.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "tslib": "^2.6.2", "typescript": "~5.3.3"}, "peerDependencies": {"date-fns": "^2.28.0 || ^3.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/gpbl"}}