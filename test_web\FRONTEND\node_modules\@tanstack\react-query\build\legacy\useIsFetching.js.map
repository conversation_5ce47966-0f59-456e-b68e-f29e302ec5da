{"version": 3, "sources": ["../../src/useIsFetching.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { notifyManager } from '@tanstack/query-core'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport type { QueryClient, QueryFilters } from '@tanstack/query-core'\n\nexport function useIsFetching(\n  filters?: QueryFilters,\n  queryClient?: QueryClient,\n): number {\n  const client = useQueryClient(queryClient)\n  const queryCache = client.getQueryCache()\n\n  return React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        queryCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [queryCache],\n    ),\n    () => client.isFetching(filters),\n    () => client.isFetching(filters),\n  )\n}\n"], "mappings": ";;;AACA,YAAY,WAAW;AACvB,SAAS,qBAAqB;AAE9B,SAAS,sBAAsB;AAGxB,SAAS,cACd,SACA,aACQ;AACR,QAAM,SAAS,eAAe,WAAW;AACzC,QAAM,aAAa,OAAO,cAAc;AAExC,SAAa;AAAA,IACL;AAAA,MACJ,CAAC,kBACC,WAAW,UAAU,cAAc,WAAW,aAAa,CAAC;AAAA,MAC9D,CAAC,UAAU;AAAA,IACb;AAAA,IACA,MAAM,OAAO,WAAW,OAAO;AAAA,IAC/B,MAAM,OAAO,WAAW,OAAO;AAAA,EACjC;AACF;", "names": []}