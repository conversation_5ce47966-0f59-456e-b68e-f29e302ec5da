{"version": 3, "sources": ["../../src/suspense.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const defaultThrowOnError = <\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  _error: TError,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n) => query.state.data === undefined\n\nexport const ensureSuspenseTimers = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Handle staleTime to ensure minimum 1000ms in Suspense mode\n    // This prevents unnecessary refetching when components remount after suspending\n\n    const clamp = (value: number | 'static' | undefined) =>\n      value === 'static' ? value : Math.max(value ?? 1000, 1000)\n\n    const originalStaleTime = defaultedOptions.staleTime\n    defaultedOptions.staleTime =\n      typeof originalStaleTime === 'function'\n        ? (...args) => clamp(originalStaleTime(...args))\n        : clamp(originalStaleTime)\n\n    if (typeof defaultedOptions.gcTime === 'number') {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1000)\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n) => defaultedOptions?.suspense && result.isPending\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer.fetchOptimistic(defaultedOptions).catch(() => {\n    errorResetBoundary.clearReset()\n  })\n"], "mappings": ";AAUO,IAAM,sBAAsB,CAMjC,QACA,UACG,MAAM,MAAM,SAAS;AAEnB,IAAM,uBAAuB,CAClC,qBACG;AACH,MAAI,iBAAiB,UAAU;AAI7B,UAAM,QAAQ,CAAC,UACb,UAAU,WAAW,QAAQ,KAAK,IAAI,SAAS,KAAM,GAAI;AAE3D,UAAM,oBAAoB,iBAAiB;AAC3C,qBAAiB,YACf,OAAO,sBAAsB,aACzB,IAAI,SAAS,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAC7C,MAAM,iBAAiB;AAE7B,QAAI,OAAO,iBAAiB,WAAW,UAAU;AAC/C,uBAAiB,SAAS,KAAK,IAAI,iBAAiB,QAAQ,GAAI;AAAA,IAClE;AAAA,EACF;AACF;AAEO,IAAM,YAAY,CACvB,QACA,gBACG,OAAO,aAAa,OAAO,cAAc,CAAC;AAExC,IAAM,gBAAgB,CAC3B,kBAGA,YACG,qDAAkB,aAAY,OAAO;AAEnC,IAAM,kBAAkB,CAO7B,kBAOA,UACA,uBAEA,SAAS,gBAAgB,gBAAgB,EAAE,MAAM,MAAM;AACrD,qBAAmB,WAAW;AAChC,CAAC;", "names": []}