{"name": "my-backend", "version": "1.0.0", "description": "Backend API", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "seed": "node scripts/seed-data.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "install": "^0.13.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "npm": "^11.4.2"}, "devDependencies": {"nodemon": "^3.0.1"}}